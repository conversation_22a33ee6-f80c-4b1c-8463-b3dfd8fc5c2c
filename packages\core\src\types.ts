import Emitter from "eventemitter3";
import React from "react";
import { RouteObject } from "react-router";

import { Message } from "./core/common/message";
import { AgentStore } from "./core/state/store";

/** -- 配置文件 -- */

/**
 * 组件配置接口
 * 定义可配置UI组件的结构
 */
export interface WidgetConfig {
  /** 组件名称 */
  code: string;
  /** 可选的组件用途描述 */
  description?: string;
  /** 渲染组件的React函数组件 */
  component: React.FC<any>;
  /** 组件的默认属性 */
  props?: Record<string, any>;
}

/**
 * 消息插槽组件配置接口
 * 定义可配置UI组件的结构
 */
export interface MessageSlotWidgetConfig extends WidgetConfig {
  role?: Role;
}

/**
 * 智能体配置接口
 *
 * 定义智能体的完整配置结构，包括UI组件、提示词、命令和API设置
 */
export interface AgentConfig {
  /** 智能体名称 */
  name: string;
  /** 智能体代码, 不能重复，只允许使用字母 数字 - _ */
  code: string;
  /** 智能体头像地址 */
  avatar?: string;
  /** logo 地址 */
  logo?: string;
  /** 欢迎词 */
  welcome?: string;
  /** 开启热门问题 */
  popularQuestion?: boolean;
  /** 可选的智能体用途或能力描述 */
  description?: string;
  /** 消息区域配置 */
  message?: {
    /** 在消息区域显示的组件 */
    blocks?: {
      widgets: WidgetConfig[];
    };
    /** 消息区域的插槽配置 */
    slots?: {
      /** 在头部插槽渲染的组件 */
      header?: {
        widgets: MessageSlotWidgetConfig[];
      };
      /** 在底部插槽渲染的组件 */
      footer?: {
        widgets: MessageSlotWidgetConfig[];
      };
    };
  };
  /** 可用的提示词模板 */
  prompts?: PromptConfig[];
  /** 可执行的命令 */
  commands?: CommandConfig[];
  /** 智能体的建议配置 */
  suggestions?: SuggestionConfig[];
  /** 消息发送器配置 */
  sender?: {
    /** 发送器区域的插槽配置 */
    slots?: {
      /** 在头部弹出面板插槽渲染的组件 */
      headerPanel?: {
        title?: string;
        widgets: WidgetConfig[];
      };
      /** 在头部插槽渲染的组件 */
      header?: {
        widgets: WidgetConfig[];
      };
      /** 在底部插槽渲染的组件 */
      footer?: {
        widgets: WidgetConfig[];
      };
    };
    headerPanel?: {
      enable?: boolean;
      height?: number;
      buttonText?: string;
      title?: string;
    };
  };
  /** 侧边面板配置 */
  sidePanel?: {
    /** 侧边面板的插槽配置 */
    slots?: {
      /** 在头部插槽渲染的组件 */
      header?: {
        widgets: MessageSlotWidgetConfig[];
      };
      /** 在底部插槽渲染的组件 */
      footer?: {
        widgets: MessageSlotWidgetConfig[];
      };
    };
    /** 在侧边面板显示的组件 */
    render: {
      widgets?: WidgetConfig[];
    };
  };
  /** API请求配置 */
  request: {
    /** 对话API请求配置 */
    chat: RequestConfig;
    /** 全局请求配置 */
    global?: RequestConfig;
  };
}

/**
 * 提示词模板配置
 * 定义可在智能体中使用的可复用提示词
 */
export interface PromptConfig {
  /** 提示词模板标题 */
  title: string;
  /** 可选的提示词描述 */
  description?: string;
  /** 提示词模板内容/文本 */
  prompt: string;
  /** 提示词模板的图标 */
  icon?: React.ReactNode;
  /** 分组 */
  group?: string;
}

/**
 * 命令配置
 * 定义可在智能体内触发的可执行命令
 */
interface CommandConfig {
  /** 命令名称 */
  name: string;
  /** 可选的命令功能描述 */
  description?: string;
  /** 命令触发时执行的函数 */
  action: (...params: any[]) => any;
}

/**
 * 建议配置
 * 定义可呈现给用户的快速建议
 */
interface SuggestionConfig {
  /** 建议名称 */
  name: string;
  /** 可选的建议描述 */
  description?: string;
  /** 建议内容/文本 */
  content: string;
}

/**
 * API请求配置
 * 定义HTTP请求的配置结构
 */
interface RequestConfig {
  /** API端点URL */
  url?: string;
  /** HTTP方法（GET, POST等） */
  method?: string;
  /** HTTP头信息，键值对形式 */
  headers?: Record<string, string>;
  /** 请求体数据 */
  body?: any;
}

/**
 * 智能体聊天配置
 * 整个聊天应用的顶层配置
 */
export interface AgentChatConfig {
  /** 智能体配置列表 */
  agents: AgentConfig[];
  /** 全局请求配置 */
  request?: RequestConfig;
}

/** -- 消息相关类型和接口 -- */

export enum EventType {
  Start = 1000,
  Loading = 1001,
  End = 1002,

  Error = 2000,
}

/** 消息块 Chunk */
export interface MessageChunk {
  /** 唯一标识符 */
  package_id: number;
  /** 消息包类型 */
  package_type: MessagePackageType;
  /** 唯一标识符 */
  chunk_id: number;
  /** 是否为最后一个块 */
  is_last: boolean;
  /** 消息内容 */
  data: string;
  /** 事件ID */
  event_id: number;
  /** 事件类型 */
  event_type: EventType;
}

/**
 * 消息包类型
 * 定义消息内容包的可能类型
 */
export enum MessagePackageType {
  None = -1,
  /** 纯文本类型 */
  Text = 0,
  /** 结构化数据类型 */
  Structured = 1,
  /** 思维过程 */
  Thinking = 2,
  /** 错误 */
  Error = 3,
}

// Structured chunk type enum values
export enum StructuredChunkType {
  HEADER = "header",
  COMMAND = "command",
}
/**
 * Command structure for structured messages
 */
export interface Command {
  name: string;
  params: Record<string, any>;
}

/**
 * Command message structure
 */
export interface CommandMessage {
  commands: Command[];
  type: string;
}

/**
 * Header message structure
 */
export interface HeaderMessage {
  conversation_id: string;
  message_id: string;
  type: string;
}

/**
 * 角色枚举
 * 定义对话中可能的角色
 */
export enum Role {
  /** 用户发送的消息 */
  HUMAN = "human",
  /** 智能体/AI发送的消息 */
  AI = "ai",
  /** 系统消息（对用户不可见） */
  SYSTEM = "system",
}

/**
 * 消息命令类型枚举
 * 定义可包含在消息中的命令类型
 */
enum MessageCommandType {
  /** 调用函数的命令 */
  Call = "call",
}

/**
 * 消息命令接口
 * 定义可嵌入消息中的命令结构
 */
interface MessageCommand {
  /** 命令类型 */
  type: MessageCommandType;
  /** 要执行的命令名称 */
  name: string;
  /** 传递给命令的参数 */
  params: Record<string, any>;
}

/**
 * 结构化消息包接口
 * 定义包含命令的消息结构
 */
export interface StructuredMessagePackage {
  type: StructuredChunkType;
  /** 要执行的命令列表 */
  commands?: MessageCommand[];
  conversation_id?: string;
  message_id?: string;
}

export interface HeaderMessagePackage {
  type: StructuredChunkType.HEADER;
  /** 要执行的命令列表 */
  conversation_id: string;
  message_id: string;
}

/** 消息包状态 */
export enum MessagePackageStatus {
  /** 消息包正在加载 */
  Loading = 0,
  /** 消息包已完成加载 */
  Finished = 1,
  /** 消息包加载出错 */
  Error = 2,
}

/**
 * 消息包
 * 定义消息中单个内容包的结构
 */
export interface IMessagePackage {
  /** 包的唯一标识符 */
  package_id: number;
  /** 消息包类型 */
  package_type: MessagePackageType;
  /** 状态 */
  status: MessagePackageStatus;
  /** 内容数据 - 可以是纯文本或结构化数据 */
  data: string;
}

/**
 * 消息接口
 * 定义对话中完整消息的结构
 */

export enum MessageStatus {
  /** 消息正在加载 */
  Loading = "loading",
  /** 消息已完成加载 */
  Finished = "finished",
  /** 消息加载出错 */
  Error = "error",
  /** 消息被取消 */
  Cancelled = "cancelled",
}

export interface IMessageContextValue {
  message: Message;
  messageState: Record<string, any>;
  setMessageState: React.Dispatch<React.SetStateAction<Record<string, any>>>;
}

/** AgentChatContext */

export interface AgentChatContextValue {
  store: AgentStore;
  hostId: string;
  config: AgentChatConfig;
  mode?: "mini" | "normal" | null;
}

/** -- 命令模块相关类型和接口 -- */

/**
 * 命令回调函数类型
 * 定义命令触发时执行的回调函数类型
 */
export type CommandCallback = (params: any) => any;

/**
 * 命令取消订阅函数类型
 * 定义取消命令订阅的函数类型
 */
export type CommandUnsubscribe = () => void;

/**
 * 命令发射器接口
 * 定义命令事件发射器的类型
 */
export interface CommandEmitter extends Emitter {}

/**
 * 命令订阅选项接口
 * 定义订阅命令时的可选配置
 */
export interface CommandSubscribeOptions {
  /** 是否只执行一次 */
  once?: boolean;
  /** 优先级，数字越大优先级越高 */
  priority?: number;
}

/**
 * 命令注册接口
 * 定义注册到系统的命令结构
 */
export interface RegisteredCommand {
  /** 命令名称 */
  name: string;
  /** 命令描述 */
  description?: string;
  /** 命令分类 */
  category?: string;
  /** 命令处理函数 */
  handler: CommandCallback;
}

/** -- Block -- */

export enum EmbeddedItemType {
  Widget = "widget",
  State = "state",
}

export enum BlockType {
  Text = "text",
  Embedded = "embedded",
  Thinking = "thinking",
  Error = "error",
}

export enum BlockStatus {
  Loading = "loading",
  Finished = "finished",
  Error = "error",
}

export interface BlockConfig {
  id: string;
  type: BlockType;
  status: BlockStatus;
  content: string; // 文本 或者 xml 或者 嵌入式组件 xml
  start: number;
  end: number;
}

export enum StateUpdateStrategy {
  Replace = "replace", // 清空 State 并覆盖
  Merge = "merge", // 合并 State, 仅覆盖指定的值
  // TODO
  /**
   * 仅适用于字符串、数组；
   * 字符串原有值后拼接新增的字符串
   * 数组原有值后追加新增的数组
   */
  IncrementalMerge = "incrementalMerge",
}

export interface StateConfig {
  set: StateSet[];
}

export interface StateSet {
  path: string;
  strategy: StateUpdateStrategy;
  value: unknown;
  type: "string" | "number" | "boolean" | "object" | "array";
}

export type EmbeddedItem = EmbeddedWidgetItem | EmbeddedStateItem;
export type EmbeddedWidgetItem = { key: string; type: EmbeddedItemType } & WidgetConfig;
export type EmbeddedStateItem = { key: string; type: EmbeddedItemType } & StateConfig;

/** -- Command -- */

export enum BuildInCommand {
  OpenSidePanel = "openSidePanel", // 打开侧边栏
  CloseSidePanel = "closeSidePanel", // 关闭侧边栏
  RenderSidePanel = "renderSidePanel", // 渲染侧边栏

  SendMessage = "sendMessage", // 发送消息
  ResendPreviousMessage = "resendPreviousMessage", // 重新发送上一条消息
  CancelChatRequest = "cancelChatRequest", // 取消上一次的请求
  NewConversationCreated = "newConversationCreated", // 新建会话

  InsertTextIntoSender = "insertTextIntoSender", // 向发送器中插入文字
  InsertTagIntoSender = "insertTagIntoSender", // 向发送器中插入标签
  InsertEditableTagIntoSender = "insertEditableTagIntoSender", // 向发送器中插入可编辑标签
  InsertSelectIntoSender = "insertSelectIntoSender", // 向发送器中插入选择器
  ClearSender = "clearSender", // 清空发送器文本
  UpdateSenderText = "updateSenderText", // 更新发送器文本

  CloseSenderHeaderPanel = "closeSenderHeaderPanel", // 关闭发送器头部面板
  UpdateExtendSenderParams = "extendSenderParams", // 扩展发送器参数
}

export interface IOpenSidePanelCommandParams {
  widgetCode: string;
  width?: number;
  widgetProps?: Record<string, unknown>;
}

export interface ISendMessageParams {
  message: string;
  agentCode: string;

  conversationId?: string;
  extendParams?: Record<string, unknown>;
  isNewConversation?: boolean;
}

export interface IRenderSidePanelParams {
  widgetCode: string;
  width?: number;
  widgetProps?: Record<string, unknown>;
}

export interface IInsertTextIntoSenderParams {
  text: string;
}

export interface IInsertTagIntoSenderParams {
  content: string;
  rawValue: string;
  tooltips?: string;
}

export interface IInsertEditableTagIntoSenderParams {
  text: string;
}

export interface IInsertSelectIntoSenderParams {
  placeholder?: string;
  options: Array<{ label: string; value: string }>;
  defaultValue?: string;
  tooltips?: string;
  disabled?: boolean;
}

export interface IUpdateSenderTextParams {
  text: string;
}

export interface IUpdateExtendSenderParams {
  setValue?: (prevValue: Record<string, unknown>) => Record<string, unknown>;
}

export type IBaseCommandParams =
  | IOpenSidePanelCommandParams
  | ISendMessageParams
  | IRenderSidePanelParams
  | IInsertTextIntoSenderParams
  | IInsertTagIntoSenderParams
  | IInsertEditableTagIntoSenderParams
  | IInsertSelectIntoSenderParams
  | IUpdateSenderTextParams
  | Record<string, unknown>;

/** -- Conversation -- */

export interface IConversation {
  id: string;
  title: string;
  // 当前会话正在使用的智能体
  currentAgentCode: string;
  group: string;
}

/** -- Response -- */
export interface StandardResponse<T = unknown> {
  code: number;
  data: T;
  pagination: {
    total: number;
    page: number;
    size: number;
    page_total: number;
  };
}

export interface StandardErrorResponse {
  code: number;
  message: string;
}

/**  -- 接口返回数据类型定义 -- */

/** 会话历史 */

export interface ConversationData {
  title: string;
  id: string;
  current_agent_code: string;
  updated_at: string;
}

export type ConversationHistoryResponse = StandardResponse<ConversationData[]>;

/** Error */

export interface ErrorMessage {
  error_message: string;
  error_type: string;
}

export enum MessageErrorCode {
  IncompletePackage = "INCOMPLETE_PACKAGE",
  InvalidChunk = "INVALID_CHUNK",
  ParsingError = "PARSING_ERROR",
  NetworkError = "NETWORK_ERROR",
  ServerError = "SERVER_ERROR",
}

/** Router */
export interface NavigateGuard {
  id: string;
  guard: (
    to: string,
    info: {
      agent: AgentConfig; // 当前智能体
      isLoadingMessage: boolean; // 是否正在加载消息
    },
  ) => Promise<boolean>;
}

export type ExtendRouterObject = RouteObject & { auth: boolean };
