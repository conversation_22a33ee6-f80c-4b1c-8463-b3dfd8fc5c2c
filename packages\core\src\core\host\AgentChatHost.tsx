import { nanoid } from "nanoid";
import React, { PropsWithChildren, useMemo } from "react";

import { createDefaultRouter } from "@/router";
import { AgentChatConfig } from "@/types";

import { AgentChatContext } from "../state/context";
import { AgentStore } from "../state/store";
import AgentCore from "./AgentCore";

export interface AgentChatHostProps {
  config: AgentChatConfig;
  router?: ReturnType<typeof createDefaultRouter>;
}

const AgentChatHost: React.FC<PropsWithChildren<AgentChatHostProps>> = (props) => {
  const { config, router, children } = props;
  const search = new URLSearchParams(window.location.search);
  const mode = search.get("mode") as any;
  const miniModeWidth = search.get("miniModeWidth");

  const contextValue = useMemo(
    () => ({
      hostId: nanoid(),
      store: new AgentStore(),
      config,
      mode,
      miniModeWidth,
    }),
    [],
  );

  return (
    <AgentChatContext.Provider value={contextValue}>
      <AgentCore router={router}>{children}</AgentCore>
    </AgentChatContext.Provider>
  );
};

export default AgentChatHost;
