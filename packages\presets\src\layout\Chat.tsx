import { useThrottleFn } from "ahooks";
import React, { useContext, useEffect, useRef } from "react";

import { AgentChatContext, BuildInCommand, useMessages, useSubscribeCommand } from "@cscs-agent/core";

interface DefaultChatLayoutProps {
  navigationBar: React.ReactNode;
  message: React.ReactNode;
  sender: React.ReactNode;
  sidePanel: React.ReactNode;
}

const DefaultChatLayout: React.FC<DefaultChatLayoutProps> = (props) => {
  const { navigationBar, message, sender, sidePanel } = props;
  const messageContainerRef = useRef<HTMLDivElement>(null);
  const scrollWrapRef = useRef<HTMLDivElement>(null);
  const [sidePanelIsOpen, setSidePanelIsOpen] = React.useState(false);
  const [disableAutoScroll, setDisableAutoScroll] = React.useState(false);
  const [messages] = useMessages();
  const { mode } = useContext(AgentChatContext);
  const width = mode === "mini" ? "400px" : "800px";

  // 自动滚动到底部
  const { run: debouncedScrollToBottom } = useThrottleFn(
    () => {
      if (disableAutoScroll) return;
      if (scrollWrapRef.current) {
        scrollWrapRef.current.scrollTo({
          top: scrollWrapRef.current.scrollHeight,
          behavior: "smooth",
        });
      }
    },
    { wait: 500 },
  );

  useEffect(() => {
    // 监听消息容器高度变化
    const messageContainer = messageContainerRef.current;
    if (!messageContainer) return;

    const resizeObserver = new ResizeObserver(() => {
      debouncedScrollToBottom();
    });
    resizeObserver.observe(messageContainer);

    return () => {
      resizeObserver.disconnect();
    };
  }, [debouncedScrollToBottom]);

  useEffect(() => {
    // 每当消息变化时，开启自动滚动
    setDisableAutoScroll(false);
  }, [messages]);

  useSubscribeCommand(BuildInCommand.OpenSidePanel, () => {
    setSidePanelIsOpen(true);
  });

  useSubscribeCommand(BuildInCommand.CloseSidePanel, () => {
    setSidePanelIsOpen(false);
  });

  return (
    <div className={`pts:flex pts:h-full pts:w-full ${sidePanelIsOpen ? "pts:bg-[#f8f9fb]" : "pts:bg-white"}`}>
      {/* Middle content - adaptive width */}
      <div className={`pts:h-full pts:flex pts:flex-col ${sidePanelIsOpen ? "" : "pts:flex-1"}`}>
        <div>{navigationBar}</div>
        <div
          ref={scrollWrapRef}
          className="pts:flex-1 pts:mx-auto pts:p-4 pts:w-full pts:overflow-y-scroll mini-scrollbar"
          onWheel={() => {
            setDisableAutoScroll(true);
          }}
        >
          <div
            ref={messageContainerRef}
            className={"pts:mx-auto pts:translate-x-[3px]"}
            style={{
              width: sidePanelIsOpen ? "40vw" : width,
            }}
          >
            {message}
          </div>
        </div>
        <div
          className={`pts:pb-4 pts:mb-4  pts:mx-auto`}
          style={{
            width: sidePanelIsOpen ? "40vw" : width,
          }}
        >
          {sender}
        </div>
      </div>

      {/* Right side panel - toggleable */}
      <div
        className={`pts:h-full pts:border-l pts:border-gray-200 pts:transition-all pts:duration-300 ${sidePanelIsOpen ? "pts:flex-1 pts:overflow-auto" : "pts:w-[0] pts:overflow-hidden"}`}
      >
        {sidePanel}
      </div>
    </div>
  );
};

export default DefaultChatLayout;
